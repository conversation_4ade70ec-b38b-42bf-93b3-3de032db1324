{"buildCommand": "pnpm build", "outputDirectory": "dist", "framework": "vite", "functions": {"api/index.js": {"maxDuration": 30}}, "rewrites": [{"source": "/api/(.*)", "destination": "/api/index.js"}, {"source": "/((?!api|_next|_static|_vercel|images|assets|.*\\.[\\w]+).*)", "destination": "/index.html"}], "headers": [{"source": "/api/(.*)", "headers": [{"key": "Cache-Control", "value": "no-cache, no-store, must-revalidate"}, {"key": "Access-Control-Allow-Origin", "value": "*"}, {"key": "Access-Control-Allow-Methods", "value": "GET, POST, PUT, DELETE, OPTIONS"}, {"key": "Access-Control-Allow-Headers", "value": "Content-Type, Authorization"}]}, {"source": "/(.*\\.html|/)", "headers": [{"key": "Cache-Control", "value": "no-cache, must-revalidate"}, {"key": "Pragma", "value": "no-cache"}]}, {"source": "/assets/.*-[a-f0-9]{8}\\.(js|css|woff2?|ttf|eot|svg|png|jpg|jpeg|gif|webp|ico)$", "headers": [{"key": "Cache-Control", "value": "public, max-age=31536000, immutable"}]}, {"source": "/images/(.*)", "headers": [{"key": "Cache-Control", "value": "public, max-age=86400"}]}]}